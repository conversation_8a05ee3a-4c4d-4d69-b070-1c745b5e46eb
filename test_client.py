#!/usr/bin/env python3
"""
简单的测试客户端，用于测试AI Review应用的IPC通信
"""

import socket
import json
import uuid
import os
import tempfile
from pathlib import Path

def get_socket_path():
    """获取socket文件路径"""
    # 尝试获取运行时目录，如果失败则使用临时目录
    runtime_dir = os.environ.get('XDG_RUNTIME_DIR')
    if runtime_dir:
        socket_dir = Path(runtime_dir)
    else:
        # macOS上通常没有XDG_RUNTIME_DIR，使用临时目录
        socket_dir = Path(tempfile.gettempdir())
    
    return socket_dir / "ai-review.sock"

def send_message(content, timeout=30):
    """发送消息到AI Review应用"""
    socket_path = get_socket_path()
    
    if not socket_path.exists():
        print(f"❌ Socket文件不存在: {socket_path}")
        return None
    
    try:
        # 创建Unix域套接字
        sock = socket.socket(socket.AF_UNIX, socket.SOCK_STREAM)
        sock.connect(str(socket_path))
        
        # 构造消息
        message = {
            "id": str(uuid.uuid4()),
            "message_type": "Request",
            "content": content,
            "timeout": timeout
        }
        
        # 发送消息
        message_json = json.dumps(message) + "\n"
        sock.send(message_json.encode('utf-8'))
        print(f"✅ 已发送消息: {content}")
        
        # 接收响应
        response_data = sock.recv(4096)
        response_text = response_data.decode('utf-8').strip()
        
        if response_text:
            try:
                response = json.loads(response_text)
                print(f"✅ 收到响应: {response}")
                return response
            except json.JSONDecodeError:
                print(f"❌ 响应格式错误: {response_text}")
                return None
        else:
            print("❌ 未收到响应")
            return None
            
    except Exception as e:
        print(f"❌ 发送消息失败: {e}")
        return None
    finally:
        try:
            sock.close()
        except:
            pass

def main():
    """主函数"""
    print("🚀 AI Review 测试客户端")
    print(f"📁 Socket路径: {get_socket_path()}")
    
    while True:
        print("\n" + "="*50)
        print("请选择操作:")
        print("1. 发送测试消息")
        print("2. 发送init命令")
        print("3. 退出")
        
        choice = input("请输入选择 (1-3): ").strip()
        
        if choice == "1":
            content = input("请输入消息内容: ").strip()
            if content:
                send_message(content)
            else:
                print("❌ 消息内容不能为空")
                
        elif choice == "2":
            print("🔧 发送init命令...")
            send_message("init")
            
        elif choice == "3":
            print("👋 再见!")
            break
            
        else:
            print("❌ 无效选择，请重新输入")

if __name__ == "__main__":
    main()
