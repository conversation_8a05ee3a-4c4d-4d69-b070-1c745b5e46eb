{"$schema": "https://schema.tauri.app/config/2.0.0", "productName": "AI Review", "version": "0.1.0", "identifier": "com.ai-review.app", "build": {"beforeDevCommand": "npm run dev", "beforeBuildCommand": "npm run build", "frontendDist": "./dist", "devUrl": "http://localhost:5173"}, "app": {"windows": [{"title": "AI Review", "width": 800, "height": 600, "minWidth": 600, "minHeight": 400, "center": true, "visible": true, "alwaysOnTop": false, "decorations": true, "transparent": false, "resizable": true, "fullscreen": false}], "security": {"csp": null}}, "bundle": {"active": false}}