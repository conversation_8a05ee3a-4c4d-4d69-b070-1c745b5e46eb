{"$schema": "https://schema.tauri.app/config/2.0.0", "productName": "AI Review", "version": "0.1.0", "identifier": "com.ai-review.app", "build": {"beforeDevCommand": "npm run dev", "beforeBuildCommand": "npm run build", "frontendDist": "./dist", "devUrl": "http://localhost:5173"}, "app": {"windows": [{"title": "AI Review", "width": 800, "height": 600, "minWidth": 600, "minHeight": 400, "center": true, "visible": true, "alwaysOnTop": false, "decorations": true, "transparent": false, "resizable": true, "fullscreen": false}], "security": {"csp": null, "capabilities": [{"identifier": "main-capability", "description": "Main window capabilities", "windows": ["main"], "permissions": ["core:event:allow-listen", "core:event:allow-emit", "core:window:allow-show", "core:window:allow-hide", "core:window:allow-close", "core:window:allow-minimize", "core:window:allow-set-focus", "core:window:allow-set-always-on-top"]}]}}, "bundle": {"active": false}}