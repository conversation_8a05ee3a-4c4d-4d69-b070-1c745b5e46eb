<script setup>
import {
  BulbOutlined,
  ClockCircleOutlined,
  CloseOutlined,
  EditOutlined,
  MailOutlined,
  SendOutlined,
} from '@ant-design/icons-vue'
import { computed, nextTick, onMounted, onUnmounted, ref } from 'vue'

const props = defineProps({
  request: {
    type: Object,
    required: true,
  },
})

const emit = defineEmits(['response', 'cancel'])

// 响应式数据
const responseText = ref('')
const isProcessing = ref(false)
const remainingTime = ref(props.request.timeout || 30)
const textareaRef = ref(null)

let timeoutInterval = null

// 计算属性
const progressPercentage = computed(() => {
  if (!props.request.timeout)
    return 100
  return (remainingTime.value / props.request.timeout) * 100
})

// 生命周期
onMounted(() => {
  // 自动聚焦到输入框
  nextTick(() => {
    if (textareaRef.value) {
      textareaRef.value.focus()
    }
  })

  // 启动倒计时
  if (props.request.timeout) {
    startCountdown()
  }

  // 添加键盘事件监听
  document.addEventListener('keydown', handleGlobalKeydown)
})

onUnmounted(() => {
  if (timeoutInterval) {
    clearInterval(timeoutInterval)
  }
  document.removeEventListener('keydown', handleGlobalKeydown)
})

// 方法
function startCountdown() {
  timeoutInterval = setInterval(() => {
    remainingTime.value--

    if (remainingTime.value <= 0) {
      clearInterval(timeoutInterval)
      // 自动取消
      handleCancel()
    }
  }, 1000)
}

function formatTime(date) {
  return date.toLocaleTimeString('zh-CN', {
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit',
  })
}

async function handleSend() {
  if (!responseText.value.trim() || isProcessing.value)
    return

  isProcessing.value = true
  try {
    emit('response', responseText.value.trim())
  }
  finally {
    isProcessing.value = false
  }
}

function handleCancel() {
  if (isProcessing.value)
    return
  emit('cancel')
}

function handleKeydown(event) {
  if ((event.ctrlKey || event.metaKey) && event.key === 'Enter') {
    event.preventDefault()
    handleSend()
  }
}

function handleGlobalKeydown(event) {
  if (event.key === 'Escape') {
    event.preventDefault()
    handleCancel()
  }
}
</script>

<template>
  <div class="request-container">
    <!-- 消息内容卡片 -->
    <div class="message-card">
      <div class="message-header">
        <MailOutlined class="message-icon" />
        <span class="message-title">收到新消息</span>
        <a-tag size="small" color="blue">
          ID: {{ request.id.slice(0, 8) }}...
        </a-tag>
      </div>
      <div class="message-content">
        {{ request.content }}
      </div>
      <div class="message-time">
        <ClockCircleOutlined />
        {{ formatTime(new Date()) }}
      </div>
    </div>

    <!-- 回复区域 -->
    <div class="reply-section">
      <div class="reply-header">
        <EditOutlined class="reply-icon" />
        <span class="reply-title">您的回复</span>
      </div>

      <a-textarea
        ref="textareaRef"
        v-model:value="responseText"
        :rows="4"
        :max-length="1000"
        show-count
        placeholder="请输入您的回复..."
        :disabled="isProcessing"
        class="reply-textarea"
        @keydown="handleKeydown"
      />

      <!-- 操作区域 -->
      <div class="action-area">
        <div class="action-left">
          <!-- 倒计时信息 -->
          <div v-if="request.timeout" class="countdown-info">
            <ClockCircleOutlined
              :spin="remainingTime <= 10"
              :style="{ color: remainingTime <= 10 ? '#ff4d4f' : '#1890ff' }"
            />
            <span
              class="countdown-text"
              :style="{ color: remainingTime <= 10 ? '#ff4d4f' : '#666' }"
            >
              剩余 {{ remainingTime }}秒
            </span>
            <a-progress
              :percent="progressPercentage"
              :status="remainingTime <= 10 ? 'exception' : 'active'"
              :stroke-color="remainingTime <= 10 ? '#ff4d4f' : '#1890ff'"
              size="small"
              :show-info="false"
              class="countdown-progress"
            />
          </div>
        </div>

        <div class="action-buttons">
          <a-button
            :disabled="isProcessing"
            @click="handleCancel"
          >
            <template #icon>
              <CloseOutlined />
            </template>
            取消
          </a-button>

          <a-button
            type="primary"
            :disabled="!responseText.trim() || isProcessing"
            :loading="isProcessing"
            @click="handleSend"
          >
            <template v-if="!isProcessing" #icon>
              <SendOutlined />
            </template>
            {{ isProcessing ? '发送中...' : '发送回复' }}
          </a-button>
        </div>
      </div>

      <!-- 快捷键提示 -->
      <div class="shortcuts-hint">
        <BulbOutlined />
        <span>快捷键: Ctrl/Cmd + Enter 发送 | Escape 取消</span>
      </div>
    </div>
  </div>
</template>

<style scoped>
.request-container {
  padding: 16px;
  display: flex;
  flex-direction: column;
  gap: 16px;
  max-height: 60vh;
  overflow: hidden;
}

/* 消息卡片 */
.message-card {
  background: #ffffff;
  border: 1px solid #e1e1e1;
  border-radius: 12px;
  padding: 16px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.message-header {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 12px;
}

.message-icon {
  color: var(--ant-primary-color, #1890ff);
  font-size: 16px;
}

.message-title {
  font-weight: 600;
  color: #262626;
  font-size: 14px;
}

.message-content {
  color: #262626;
  line-height: 1.6;
  font-size: 14px;
  word-wrap: break-word;
  white-space: pre-wrap;
  margin-bottom: 12px;
  padding: 12px;
  background: #f9f9f9;
  border-radius: 8px;
  border-left: 3px solid var(--ant-primary-color, #1890ff);
}

.message-time {
  display: flex;
  align-items: center;
  gap: 4px;
  color: #8c8c8c;
  font-size: 12px;
}

/* 回复区域 */
.reply-section {
  background: #ffffff;
  border: 1px solid #e1e1e1;
  border-radius: 12px;
  padding: 16px;
  flex: 1;
  display: flex;
  flex-direction: column;
}

.reply-header {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 12px;
}

.reply-icon {
  color: var(--ant-primary-color, #1890ff);
  font-size: 16px;
}

.reply-title {
  font-weight: 600;
  color: #262626;
  font-size: 14px;
}

.reply-textarea {
  margin-bottom: 16px;
  border-radius: 8px;
  resize: none;
}

/* 操作区域 */
.action-area {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.action-left {
  flex: 1;
}

.countdown-info {
  display: flex;
  align-items: center;
  gap: 8px;
}

.countdown-text {
  font-size: 13px;
  font-weight: 500;
}

.countdown-progress {
  width: 120px;
  margin-left: 8px;
}

.action-buttons {
  display: flex;
  gap: 12px;
  align-items: center;
}

/* 快捷键提示 */
.shortcuts-hint {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 6px;
  color: #8c8c8c;
  font-size: 12px;
  padding-top: 8px;
  border-top: 1px solid #f0f0f0;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .action-area {
    flex-direction: column;
    align-items: stretch;
    gap: 12px;
  }

  .action-left {
    order: 2;
  }

  .action-buttons {
    order: 1;
    justify-content: flex-end;
  }

  .countdown-info {
    justify-content: center;
  }
}

@media (max-height: 600px) {
  .request-container {
    max-height: 50vh;
  }

  .reply-textarea {
    min-height: 60px;
  }
}
</style>
