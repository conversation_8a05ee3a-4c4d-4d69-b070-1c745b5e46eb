<script setup>
import {
  ClockCircleOutlined,
  MessageOutlined,
  RobotOutlined,
  SettingOutlined,
} from '@ant-design/icons-vue'
import { listen } from '@tauri-apps/api/event'
import { invoke } from '@tauri-apps/api/core'
import { nextTick, onMounted, ref } from 'vue'
import RequestHandler from './components/RequestHandler.vue'
import SettingsModal from './components/SettingsModal.vue'

// 响应式数据
const appInfo = ref('')
const currentRequest = ref(null)
const isConnected = ref(false)
const chatHistory = ref([])
const chatHistoryRef = ref(null)
const showSettings = ref(false)

// 聊天历史管理（限制数量以优化性能）
const MAX_HISTORY_ITEMS = 100

function addToHistory(type, content, id = null) {
  const message = {
    id: id || Date.now().toString(),
    type, // 'incoming' 或 'outgoing'
    content,
    timestamp: new Date(),
  }

  chatHistory.value.push(message)

  // 限制历史记录数量
  if (chatHistory.value.length > MAX_HISTORY_ITEMS) {
    chatHistory.value.shift()
  }

  // 滚动到底部
  nextTick(() => {
    if (chatHistoryRef.value) {
      chatHistoryRef.value.scrollTop = chatHistoryRef.value.scrollHeight
    }
  })
}

function formatTime(date) {
  return date.toLocaleTimeString('zh-CN', {
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit',
  })
}



// 检查IPC连接状态
async function checkConnectionStatus() {
  try {
    const status = await invoke('check_ipc_status')
    isConnected.value = status
    return status
  }
  catch (error) {
    console.error('❌ 检查IPC状态失败:', error)
    isConnected.value = false
    return false
  }
}

// 初始化应用
onMounted(async () => {
  console.warn('🚀 AI Review Vue App 初始化中...')
  console.warn('🔧 Tauri API 可用性检查:', !!window.__TAURI__)

  try {
    // 获取应用信息
    appInfo.value = await invoke('get_app_info')
    console.warn('✅ 应用信息获取成功:', appInfo.value)
  }
  catch (error) {
    console.error('❌ 获取应用信息失败:', error)
    appInfo.value = 'AI Review App v0.1.0'
  }

  // 监听新请求事件
  try {
    console.warn('🔧 开始设置事件监听器...')
    await listen('new-request', (event) => {
      console.warn('🎯 收到新请求事件:', event)
      const message = event.payload
      console.warn('📨 解析后的消息:', message)

      // 添加到聊天历史
      addToHistory('incoming', message.content, message.id)

      // 设置当前请求
      currentRequest.value = message
      console.warn('📨 currentRequest已更新:', currentRequest.value)
    })
    console.warn('✅ 事件监听器设置成功')
  }
  catch (error) {
    console.error('❌ 设置事件监听器失败:', error)
  }

  // 初始检查连接状态
  await checkConnectionStatus()

  // 定期检查连接状态（每5秒）
  setInterval(checkConnectionStatus, 5000)
})

// 处理用户回复
async function handleResponse(response) {
  if (!currentRequest.value)
    return

  try {
    // 添加回复到聊天历史
    addToHistory('outgoing', response)

    await invoke('respond_to_request', {
      id: currentRequest.value.id,
      response,
    })
    console.warn('✅ 回复发送成功:', response)
    currentRequest.value = null
  }
  catch (error) {
    console.error('❌ 发送回复失败:', error)
    console.error(`发送回复失败: ${error}`)
  }
}

// 处理取消操作
async function handleCancel() {
  if (!currentRequest.value)
    return

  try {
    // 添加取消信息到聊天历史
    addToHistory('outgoing', '[已取消]')

    await invoke('respond_to_request', {
      id: currentRequest.value.id,
      response: '[用户取消了请求]',
    })
    console.warn('✅ 请求已取消')
    currentRequest.value = null
  }
  catch (error) {
    console.error('❌ 取消请求失败:', error)
  }
}

// 打开设置弹窗
function openSettings() {
  showSettings.value = true
}

// 关闭设置弹窗
function closeSettings() {
  showSettings.value = false
}


</script>

<template>
  <div class="app-container">
    <!-- 状态栏 -->
    <div class="status-bar">
      <div class="status-content">
        <div class="app-title">
          <RobotOutlined class="app-icon" />
          <span class="app-name">AI Review</span>
        </div>
        <div class="status-indicator">
          <a-badge
            :status="isConnected ? 'success' : 'error'"
            :text="isConnected ? '已连接' : '连接中...'"
            class="status-badge"
          />
        </div>
      </div>
      <div class="toolbar">
        <a-button
          type="text"
          size="small"
          class="tool-btn"
          @click="openSettings"
          title="设置"
        >
          <SettingOutlined />
        </a-button>
      </div>
    </div>

    <!-- 主要内容区域 -->
    <div class="main-content">
      <!-- 聊天历史区域 -->
      <div ref="chatHistoryRef" class="chat-history">
        <a-empty
          v-if="chatHistory.length === 0"
          class="empty-state"
          description="暂无聊天记录"
        >
          <template #image>
            <MessageOutlined class="empty-icon" />
          </template>
          <template #description>
            <span class="empty-description">
              暂无聊天记录<br />
              <small>等待命令行消息...</small>
            </span>
          </template>
        </a-empty>

        <div v-else class="messages">
          <div
            v-for="message in chatHistory"
            :key="message.id"
            class="message-item fade-in-up"
            :class="message.type"
          >
            <a-card
              :bordered="false"
              size="small"
              class="message-card"
              :class="`message-${message.type}`"
            >
              <div class="message-content">
                <div class="message-text">
                  {{ message.content }}
                </div>
                <div class="message-time">
                  <ClockCircleOutlined class="time-icon" />
                  {{ formatTime(message.timestamp) }}
                </div>
              </div>
            </a-card>
          </div>
        </div>
      </div>

      <!-- 当前请求处理区域 -->
      <div v-if="currentRequest" class="current-request slide-in-right">
        <RequestHandler
          :request="currentRequest"
          @response="handleResponse"
          @cancel="handleCancel"
        />
      </div>
    </div>

    <!-- 设置弹窗 -->
    <SettingsModal
      v-model:visible="showSettings"
      @close="closeSettings"
    />
  </div>
</template>

<style scoped>
.app-container {
  width: 100%;
  height: 100vh;
  display: flex;
  flex-direction: column;
  background: #ffffff;
  overflow: hidden;
  position: relative;
}

/* 状态栏 */
.status-bar {
  height: 60px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border: none;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 24px;
  user-select: none;
  flex-shrink: 0;
  box-shadow: 0 4px 20px rgba(102, 126, 234, 0.3);
  position: relative;
  overflow: hidden;
}

.status-bar::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0.05) 100%);
  pointer-events: none;
}

.status-content {
  display: flex;
  align-items: center;
  gap: 24px;
  position: relative;
  z-index: 1;
}

.app-title {
  display: flex;
  align-items: center;
  gap: 12px;
  color: white;
  font-weight: 700;
  font-size: 18px;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.app-icon {
  font-size: 24px;
  color: #fbbf24;
  filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.2));
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0%, 100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
}

.app-name {
  color: white;
  letter-spacing: 0.5px;
}

.status-indicator {
  display: flex;
  align-items: center;
  position: relative;
  z-index: 1;
}

.status-badge :deep(.ant-badge-status-text) {
  color: rgba(255, 255, 255, 0.9);
  font-size: 14px;
  font-weight: 600;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
}

.status-badge :deep(.ant-badge-status-dot) {
  width: 10px;
  height: 10px;
  border: 2px solid rgba(255, 255, 255, 0.3);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
}

.status-badge :deep(.ant-badge-status-success) {
  background-color: #10b981;
  box-shadow: 0 0 12px rgba(16, 185, 129, 0.6);
}

.status-badge :deep(.ant-badge-status-error) {
  background-color: #ef4444;
  box-shadow: 0 0 12px rgba(239, 68, 68, 0.6);
}

.toolbar {
  display: flex;
  gap: 8px;
  position: relative;
  z-index: 1;
}

.tool-btn {
  width: 40px;
  height: 40px;
  border-radius: 12px;
  color: rgba(255, 255, 255, 0.8) !important;
  background: rgba(255, 255, 255, 0.1) !important;
  border: 1px solid rgba(255, 255, 255, 0.2) !important;
  backdrop-filter: blur(10px);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  font-size: 16px;
}

.tool-btn:hover {
  background: rgba(255, 255, 255, 0.2) !important;
  color: white !important;
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
  border-color: rgba(255, 255, 255, 0.4) !important;
}

/* 主要内容区域 */
.main-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  min-height: 0;
}

/* 聊天历史区域 */
.chat-history {
  flex: 1;
  overflow-y: auto;
  padding: 20px;
  background: #f9fafb;
  min-height: 200px;
  max-height: 50vh;
}

.empty-state {
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.empty-state :deep(.ant-empty-image) {
  margin-bottom: 16px;
}

.empty-icon {
  font-size: 48px;
  color: #9ca3af;
}

.empty-description {
  color: #6b7280;
  font-size: 16px;
}

.empty-description small {
  font-size: 12px;
  opacity: 0.7;
}

/* 消息列表 */
.messages {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.message-item {
  display: flex;
  max-width: 80%;
}

.message-item.incoming {
  align-self: flex-start;
}

.message-item.outgoing {
  align-self: flex-end;
}

.message-card {
  width: 100%;
  border-radius: 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.message-incoming .message-card {
  background: #ffffff;
  border: 1px solid #e5e7eb;
}

.message-outgoing .message-card {
  background: #3b82f6;
  border: 1px solid #3b82f6;
}

.message-outgoing .message-content {
  color: white;
}

.message-outgoing .message-time {
  color: rgba(255, 255, 255, 0.8);
}

.message-content {
  padding: 0;
}

.message-text {
  font-size: 14px;
  line-height: 1.5;
  word-wrap: break-word;
  margin-bottom: 8px;
}

.message-time {
  font-size: 11px;
  opacity: 0.7;
  display: flex;
  align-items: center;
  gap: 4px;
  justify-content: flex-end;
}

.time-icon {
  font-size: 10px;
}

/* 当前请求区域 */
.current-request {
  background: #ffffff;
  border-top: 1px solid #e5e7eb;
  flex: 1;
  display: flex;
  flex-direction: column;
  min-height: 400px;
  max-height: 70vh;
  overflow: hidden;
}

/* 滚动条样式 */
.chat-history::-webkit-scrollbar {
  width: 6px;
}

.chat-history::-webkit-scrollbar-track {
  background: #f3f4f6;
  border-radius: 3px;
}

.chat-history::-webkit-scrollbar-thumb {
  background: #d1d5db;
  border-radius: 3px;
}

.chat-history::-webkit-scrollbar-thumb:hover {
  background: #9ca3af;
}
</style>
